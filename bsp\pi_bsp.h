#ifndef __PI_BSP_H__
#define __PI_BSP_H__

#include "bsp_system.h"

// ???????
#define RED_LASER_ID 'R'
#define GREEN_LASER_ID 'G'

// ????????
typedef struct {
    char type;    // ????: 'R'??????,'G'??????
    int x;        // X??
    int y;        // Y??
    uint8_t isValid; // ?????:????????/???
} LaserCoord_t;

// ??PID????
#define NORMAL_X_KP     3.0f    // ??????x?Kp?
#define BOOST_X_KP      6.0f    // ??????x?Kp?(???)
#define BOOST_CONDITION_X   0   // ?????x???
#define BOOST_CONDITION_Y   0   // ?????y???

// ????
int pi_parse_data(char *buffer);
void pi_proc(void);
void update_x_pid_gain(void);

extern LaserCoord_t latest_red_laser_coord;
extern LaserCoord_t latest_green_laser_coord;

#endif