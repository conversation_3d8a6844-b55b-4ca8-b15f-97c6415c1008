# 摄像头坐标(0,0)时x轴电机加速优化指南

## 📋 项目概述

**目标**：当摄像头发送坐标为(0,0)时，让x轴电机转得更快，同时保持现有PID的稳定性。

**方案**：动态PID增益调整 - 在特定条件下临时提高x轴PID的P增益。

---

## 🔍 原理说明

### 当前工作机制
1. 摄像头发送红色激光坐标 `latest_red_laser_coord.x`
2. PID控制器计算误差：`error = 目标值 - 当前值`
3. 当坐标为(0,0)时，如果目标值也是0，误差很小，电机响应慢

### 优化原理
- **检测条件**：当 `latest_red_laser_coord.x == 0` 且 `latest_red_laser_coord.y == 0`
- **动作**：临时将x轴PID的Kp从3提高到6（或其他值）
- **恢复**：条件不满足时，自动恢复原始Kp=3

---

## 📝 代码修改步骤

### 第一步：修改 `bsp/pi_bsp.h` 文件

在文件末尾的 `#endif` 之前添加以下代码：

```c
// 动态PID增益配置
#define NORMAL_X_KP     3.0f    // 正常情况下的x轴Kp值
#define BOOST_X_KP      6.0f    // 加速情况下的x轴Kp值（可调整）
#define BOOST_CONDITION_X   0   // 触发加速的x坐标值
#define BOOST_CONDITION_Y   0   // 触发加速的y坐标值

// 函数声明
void update_x_pid_gain(void);
```

**修改后的完整文件应该是这样：**
```c
#ifndef __PI_BSP_H__
#define __PI_BSP_H__

#include "bsp_system.h"

// 激光类型标识符
#define RED_LASER_ID 'R'
#define GREEN_LASER_ID 'G'

// 激光坐标数据结构
typedef struct {
    char type;    // 激光类型: 'R'表示红色激光，'G'表示绿色激光
    int x;        // X坐标
    int y;        // Y坐标
    uint8_t isValid; // 有效性指示：当前坐标是否有效/已更新
} LaserCoord_t;

// 动态PID增益配置
#define NORMAL_X_KP     3.0f    // 正常情况下的x轴Kp值
#define BOOST_X_KP      6.0f    // 加速情况下的x轴Kp值（可调整）
#define BOOST_CONDITION_X   0   // 触发加速的x坐标值
#define BOOST_CONDITION_Y   0   // 触发加速的y坐标值

// 函数声明
int pi_parse_data(char *buffer);
void pi_proc(void);
void update_x_pid_gain(void);

extern LaserCoord_t latest_red_laser_coord;
extern LaserCoord_t latest_green_laser_coord;

#endif
```

### 第二步：修改 `bsp/pi_bsp.c` 文件

#### 2.1 在文件开头添加新函数

在 `pi_parse_data` 函数之前添加：

```c
/**
 * @brief 根据当前坐标动态调整x轴PID增益
 * 当检测到(0,0)坐标时，提高x轴响应速度
 */
void update_x_pid_gain(void)
{
    // 检查是否满足加速条件
    if (latest_red_laser_coord.x == BOOST_CONDITION_X && 
        latest_red_laser_coord.y == BOOST_CONDITION_Y)
    {
        // 条件满足：使用加速增益
        if (pid_x.p != BOOST_X_KP)
        {
            pid_x.p = BOOST_X_KP;
            // 可选：打印调试信息
            // my_printf(&huart1, "X轴PID加速模式：Kp=%.1f\r\n", BOOST_X_KP);
        }
    }
    else
    {
        // 条件不满足：恢复正常增益
        if (pid_x.p != NORMAL_X_KP)
        {
            pid_x.p = NORMAL_X_KP;
            // 可选：打印调试信息
            // my_printf(&huart1, "X轴PID正常模式：Kp=%.1f\r\n", NORMAL_X_KP);
        }
    }
}
```

#### 2.2 修改 `pi_proc` 函数

将原来的 `pi_proc` 函数：

```c
void pi_proc(void)
{
    float pos_out_x,pos_out_y=0;

    pos_out_x = pid_calc(&pid_x,latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
    pos_out_y = pid_calc(&pid_y,latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
    Step_Motor_Set_Speed_my(-pos_out_x,pos_out_y);
}
```

**修改为：**

```c
void pi_proc(void)
{
    float pos_out_x,pos_out_y=0;

    // 动态调整x轴PID增益
    update_x_pid_gain();

    pos_out_x = pid_calc(&pid_x,latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
    pos_out_y = pid_calc(&pid_y,latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
    Step_Motor_Set_Speed_my(-pos_out_x,pos_out_y);
}
```

---

## 🔧 参数调优指南

### 基础参数说明

| 参数 | 当前值 | 建议范围 | 说明 |
|------|--------|----------|------|
| `NORMAL_X_KP` | 3.0f | 保持不变 | 您当前稳定的PID参数 |
| `BOOST_X_KP` | 6.0f | 4.0f - 8.0f | 加速时的增益，建议从6.0开始测试 |
| `BOOST_CONDITION_X` | 0 | 0 | 触发条件的x坐标 |
| `BOOST_CONDITION_Y` | 0 | 0 | 触发条件的y坐标 |

### 调优步骤

1. **初始测试**：使用 `BOOST_X_KP = 6.0f`
2. **观察效果**：
   - 如果响应还不够快 → 增加到 7.0f 或 8.0f
   - 如果出现震荡 → 减少到 5.0f 或 4.5f
3. **稳定性测试**：确保在正常坐标下PID仍然稳定

---

## 🚀 编译和测试

### 编译步骤
1. 保存修改的文件
2. 在Keil中重新编译项目
3. 下载到开发板

### 测试方法
1. **正常模式测试**：发送非(0,0)坐标，确认x轴PID保持原有稳定性
2. **加速模式测试**：发送(0,0)坐标，观察x轴是否响应更快
3. **切换测试**：在(0,0)和其他坐标间切换，确认PID能正确切换

### 调试信息（可选）
如果需要查看PID切换过程，可以取消注释 `update_x_pid_gain` 函数中的打印语句：

```c
my_printf(&huart1, "X轴PID加速模式：Kp=%.1f\r\n", BOOST_X_KP);
my_printf(&huart1, "X轴PID正常模式：Kp=%.1f\r\n", NORMAL_X_KP);
```

---

## ⚠️ 注意事项

1. **备份原文件**：修改前请备份 `pi_bsp.h` 和 `pi_bsp.c`
2. **渐进调试**：建议先用较小的增益增量测试
3. **稳定性优先**：如果出现震荡，立即降低 `BOOST_X_KP` 值
4. **系统影响**：此修改只影响x轴PID，y轴保持不变

---

## 🎯 预期效果

- ✅ 当摄像头发送(0,0)坐标时，x轴电机响应速度提升约2倍
- ✅ 非(0,0)坐标时，保持原有的稳定PID性能
- ✅ 自动切换，无需手动干预
- ✅ 代码简洁，易于维护

**完成后，您的系统将在保持稳定性的同时，在特定条件下获得更快的x轴响应速度！**
